# main.py
from fastapi import FastAPI
from app.web.routes import router as web_router
from app.scheduler.scheduler import Scheduler
from app.scraping.scraper import DonghuaScraper
from fastapi.staticfiles import StaticFiles
import os
from config import Config
import logging

def create_app():
    app = FastAPI()
    app.include_router(web_router)
    app.mount("/static", StaticFiles(directory=os.path.join("app", "web", "static")), name="static")
    return app

app = create_app()

# Initialize the scheduler
scheduler = Scheduler()

def run_scraper():
    """
    Function to instantiate and run the scraper.
    This function will be scheduled to run at specified times.
    """
    try:
        scraper = DonghuaScraper()
        scraper.scrape()
    except Exception as e:
        logging.error(f"Scheduled scraping failed: {e}")

# Add the scraping job to the scheduler
scheduler.add_job(run_scraper, Config.SCRAPER_SCHEDULE_TIME)

# Start the scheduler
scheduler.start()

# Run the app using Uvicorn if executed directly
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000)
