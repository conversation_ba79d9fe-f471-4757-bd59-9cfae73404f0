// app/web/static/js/realtime.js
/**
 * Real-time WebSocket client for DonghuaStream Manager
 * Handles live updates for scraping events and episode changes
 */

class RealtimeClient {
    constructor() {
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.isConnected = false;
        this.currentPage = this.getCurrentPage();
        this.lastScrapeStats = null; // Store the latest scrape statistics

        this.init();
    }
    
    init() {
        this.loadStoredStats();
        this.connect();
        this.setupEventListeners();
        this.createStatusIndicator();
        this.createUpdateWidget();
    }

    loadStoredStats() {
        // Load the last scrape stats from localStorage
        try {
            const stored = localStorage.getItem('lastScrapeStats');
            if (stored) {
                this.lastScrapeStats = JSON.parse(stored);
                console.log('Loaded stored scrape stats:', this.lastScrapeStats);
            }
        } catch (error) {
            console.error('Error loading stored stats:', error);
        }
    }

    saveStats() {
        // Save the current stats to localStorage
        try {
            if (this.lastScrapeStats) {
                localStorage.setItem('lastScrapeStats', JSON.stringify(this.lastScrapeStats));
            }
        } catch (error) {
            console.error('Error saving stats:', error);
        }
    }
    
    getCurrentPage() {
        const path = window.location.pathname;
        if (path === '/') return 'home';
        if (path === '/calendar') return 'calendar';
        if (path.startsWith('/show/')) return 'show_detail';
        if (path === '/not-watching') return 'not_watching';
        return 'unknown';
    }
    
    connect() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws?page=${this.currentPage}`;
            
            this.socket = new WebSocket(wsUrl);
            
            this.socket.onopen = (event) => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.reconnectDelay = 1000;
                this.updateConnectionStatus('connected');
            };
            
            this.socket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };
            
            this.socket.onclose = (event) => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.updateConnectionStatus('disconnected');
                this.attemptReconnect();
            };
            
            this.socket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus('error');
            };
            
        } catch (error) {
            console.error('Error creating WebSocket connection:', error);
            this.updateConnectionStatus('error');
        }
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectDelay);
            
            // Exponential backoff
            this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
        } else {
            console.log('Max reconnection attempts reached');
            this.updateConnectionStatus('failed');
        }
    }
    
    handleMessage(message) {
        console.log('Received WebSocket message:', message);
        
        switch (message.type) {
            case 'connection_status':
                this.handleConnectionStatus(message);
                break;
            case 'scrape_update':
                this.handleScrapeUpdate(message);
                break;
            case 'episode_update':
                this.handleEpisodeUpdate(message);
                break;
            case 'pong':
                // Handle ping/pong for connection health
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    
    handleConnectionStatus(message) {
        console.log('Connection status:', message.status);
        this.updateConnectionStatus(message.status);
    }
    
    handleScrapeUpdate(message) {
        console.log('Scrape update received:', message);

        // Store the latest scrape statistics
        this.lastScrapeStats = {
            stats: message.stats || {},
            timestamp: message.timestamp,
            summary: message.summary
        };

        // Save to localStorage for persistence across page reloads
        this.saveStats();

        // Update the sidebar widget with scrape data
        this.updateScrapeWidget(message);

        // Show notification if new episodes were actually added to the database
        const stats = message.stats || {};
        if (stats.new_episodes > 0) {
            this.showNotification(`🎉 ${stats.new_episodes} new episode${stats.new_episodes > 1 ? 's' : ''} added!`);
        } else if (stats.updated_episodes > 0) {
            this.showNotification(`📝 ${stats.updated_episodes} preview${stats.updated_episodes > 1 ? 's' : ''} updated!`);
        } else if (stats.status === 'no_episodes') {
            this.showNotification(`📭 No new episodes found`, 'info');
        }

        // Refresh page content if needed
        if (message.action === 'refresh_content') {
            // Add a small delay to allow database updates to complete
            setTimeout(() => {
                this.refreshPageContent();
            }, 1000);
        }
    }
    
    handleEpisodeUpdate(message) {
        console.log('Episode update received:', message);

        // For episode updates, we only refresh content - NO widget updates
        // The scrape widget should only show scraping statistics
        setTimeout(() => {
            this.refreshPageContent();
        }, 500);
    }
    
    refreshPageContent() {
        // Refresh specific parts of the page based on current page
        switch (this.currentPage) {
            case 'home':
                this.refreshHomeContent();
                break;
            case 'calendar':
                this.refreshCalendarContent();
                break;
            case 'show_detail':
                this.refreshShowContent();
                break;
            default:
                // For other pages, just reload
                window.location.reload();
        }
    }
    
    refreshHomeContent() {
        // Reload the entire page for home - simplest approach
        window.location.reload();
    }
    
    refreshCalendarContent() {
        // Reload the entire page for calendar - simplest approach
        window.location.reload();
    }
    
    refreshShowContent() {
        // Reload the entire page for show detail - simplest approach
        window.location.reload();
    }
    
    updateConnectionStatus(status) {
        const indicator = document.getElementById('ws-status-indicator');
        if (!indicator) return;
        
        // Remove all status classes
        indicator.classList.remove('ws-connected', 'ws-disconnected', 'ws-error', 'ws-failed');
        
        // Add appropriate status class
        indicator.classList.add(`ws-${status}`);
        
        // Update tooltip
        const statusText = {
            'connected': 'Real-time updates active',
            'disconnected': 'Reconnecting...',
            'error': 'Connection error',
            'failed': 'Connection failed'
        };
        
        indicator.title = statusText[status] || 'Unknown status';
    }
    
    updateScrapeWidget(message) {
        const widget = document.getElementById('scrape-update-widget');
        if (!widget) return;

        // Use the shared rendering function to update the widget
        this.renderWidgetWithStats(widget, {
            stats: message.stats || {},
            timestamp: message.timestamp,
            summary: message.summary
        });

        // Add animation class to show the update
        widget.classList.add('scrape-update-flash');
        setTimeout(() => {
            widget.classList.remove('scrape-update-flash');
        }, 2000);
    }
    
    createStatusIndicator() {
        // Create connection status indicator
        const indicator = document.createElement('div');
        indicator.id = 'ws-status-indicator';
        indicator.className = 'ws-status-indicator ws-disconnected';
        indicator.title = 'Connecting...';
        indicator.innerHTML = '●';
        
        // Add to sidebar
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.appendChild(indicator);
        }
    }
    
    createUpdateWidget() {
        // Create scrape update widget
        const widget = document.createElement('div');
        widget.id = 'scrape-update-widget';
        widget.className = 'scrape-update-widget';

        // Check if we have stored stats to display
        if (this.lastScrapeStats) {
            this.renderWidgetWithStats(widget, this.lastScrapeStats);
        } else {
            // Default content when no stats are available
            widget.innerHTML = `
                <div class="scrape-update-content">
                    <div class="scrape-update-header">
                        <span class="scrape-update-icon">📊</span>
                        <span class="scrape-update-title">Waiting for updates...</span>
                    </div>
                </div>
            `;
        }

        // Add to sidebar after actions section
        const actionsSection = document.querySelector('.nav-section');
        if (actionsSection) {
            actionsSection.parentNode.insertBefore(widget, actionsSection.nextSibling);
        }
    }

    renderWidgetWithStats(widget, scrapeData) {
        const stats = scrapeData.stats || {};
        const timestamp = new Date(scrapeData.timestamp).toLocaleTimeString();

        // Determine status class and icon
        let statusClass = 'stat-success';
        let statusIcon = '🔄';

        if (stats.status === 'error') {
            statusClass = 'stat-error';
            statusIcon = '❌';
        } else if (stats.status === 'no_episodes') {
            statusClass = 'stat-warning';
            statusIcon = '📭';
        } else if (stats.new_episodes > 0) {
            statusIcon = '🎉';
        }

        // Build stats HTML - only show non-zero values to keep it clean
        let statsHtml = `
            <div class="stat-item">
                <span class="stat-label">New Episodes:</span>
                <span class="stat-value">${stats.new_episodes || 0}</span>
            </div>
        `;

        if (stats.updated_episodes > 0) {
            statsHtml += `
                <div class="stat-item">
                    <span class="stat-label">Updated:</span>
                    <span class="stat-value">${stats.updated_episodes}</span>
                </div>
            `;
        }

        if (stats.skipped_episodes > 0) {
            statsHtml += `
                <div class="stat-item">
                    <span class="stat-label">Skipped:</span>
                    <span class="stat-value">${stats.skipped_episodes}</span>
                </div>
            `;
        }

        statsHtml += `
            <div class="stat-item">
                <span class="stat-label">Time:</span>
                <span class="stat-value">${timestamp}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Status:</span>
                <span class="stat-value ${statusClass}">${stats.status || 'completed'}</span>
            </div>
        `;

        widget.innerHTML = `
            <div class="scrape-update-content">
                <div class="scrape-update-header">
                    <span class="scrape-update-icon">${statusIcon}</span>
                    <span class="scrape-update-title">Latest Scrape</span>
                </div>
                <div class="scrape-update-stats">
                    ${statsHtml}
                </div>
            </div>
        `;
    }
    
    setupEventListeners() {
        // Send ping every 30 seconds to keep connection alive
        setInterval(() => {
            if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {
                this.socket.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000);
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && !this.isConnected) {
                this.connect();
            }
        });
    }
    
    showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `scrape-notification ${type}`;
        notification.innerHTML = `
            ${message}
            <span class="notification-close">&times;</span>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);

        // Handle close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }

    // Public method to send messages
    send(message) {
        if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
        }
    }
}

// Initialize the realtime client when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.realtimeClient = new RealtimeClient();
});
